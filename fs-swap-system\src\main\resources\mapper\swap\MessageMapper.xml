<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.MessageMapper">

    <resultMap type="Message" id="MessageResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="type"    column="type"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="avatar"    column="avatar"    />
        <result property="isRead"    column="is_read"    />
        <result property="jumpType"    column="jump_type"    />
        <result property="jumpUrl"    column="jump_url"    />
        <result property="relatedId"    column="related_id"    />
        <result property="actionText"    column="action_text"    />
        <result property="fromUserId"    column="from_user_id"    />
        <result property="conversationId"    column="conversation_id"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.fs.swap.common.core.domain.dto.ConversationListDTO" id="ConversationListResult">
        <result property="conversationId" column="conversationId" />
        <result property="type" column="type" />
        <result property="title" column="title" />
        <result property="avatar" column="avatar" />
        <result property="lastMessageId" column="lastMessageId" />
        <result property="lastMessageContent" column="lastMessageContent" />
        <result property="lastMessageTime" column="lastMessageTime" />
        <result property="unreadCount" column="unreadCount" />
        <result property="isMuted" column="isMuted" />
        <result property="updateTime" column="updateTime" />
    </resultMap>

    <!-- 未读消息数量统计结果映射 -->
    <resultMap type="com.fs.swap.common.core.domain.dto.UnreadCountDTO" id="UnreadCountResult">
        <result property="type" column="type" />
        <result property="count" column="count" />
    </resultMap>

    <sql id="selectMessageVo">
        select id, user_id, type, title, content, avatar, is_read, jump_type, jump_url, related_id, action_text, from_user_id, conversation_id, status, create_time, update_time from message
    </sql>

    <select id="selectMessageList" parameterType="Message" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
            <if test="title != null and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="content != null and content != ''">and content like concat('%', #{content}, '%')</if>
            <if test="isRead != null">and is_read = #{isRead}</if>
            <if test="jumpType != null and jumpType != ''">and jump_type = #{jumpType}</if>
            <if test="fromUserId != null">and from_user_id = #{fromUserId}</if>
            <if test="conversationId != null and conversationId != ''">and conversation_id = #{conversationId}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectUserMessageList" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        where user_id = #{userId} and status = 0
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="selectMessageById" parameterType="String" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        where id = #{id}
    </select>

    <select id="selectUserMessageListWithPage" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        where user_id = #{userId} and status = 0
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countUserMessages" resultType="long">
        select count(*) from message 
        where user_id = #{userId} and status = 0
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
    </select>

    <select id="getUserUnreadCount" resultMap="UnreadCountResult">
        select type, count(*) as count 
        from message 
        where user_id = #{userId} and is_read = 0 and status = 0
        group by type
    </select>

    <select id="searchMessages" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        where user_id = #{userId} and status = 0
        and (title like concat('%', #{keyword}, '%') or content like concat('%', #{keyword}, '%'))
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="searchMessagesList" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        where user_id = #{userId} and status = 0
        and (title like concat('%', #{keyword}, '%') or content like concat('%', #{keyword}, '%'))
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        ORDER BY create_time DESC
    </select>

    <insert id="insertMessage" parameterType="Message">
        insert into message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="avatar != null">avatar,</if>
            <if test="isRead != null">is_read,</if>
            <if test="jumpType != null">jump_type,</if>
            <if test="jumpUrl != null">jump_url,</if>
            <if test="relatedId != null">related_id,</if>
            <if test="actionText != null">action_text,</if>
            <if test="fromUserId != null">from_user_id,</if>
            <if test="conversationId != null">conversation_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="jumpType != null">#{jumpType},</if>
            <if test="jumpUrl != null">#{jumpUrl},</if>
            <if test="relatedId != null">#{relatedId},</if>
            <if test="actionText != null">#{actionText},</if>
            <if test="fromUserId != null">#{fromUserId},</if>
            <if test="conversationId != null">#{conversationId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="batchInsertMessages" parameterType="java.util.List">
        insert into message (id, user_id, type, title, content, avatar, is_read, jump_type, jump_url, related_id, action_text, from_user_id, conversation_id, status, create_time)
        values
        <foreach collection="messages" item="message" separator=",">
            (#{message.id}, #{message.userId}, #{message.type}, #{message.title}, #{message.content}, 
             #{message.avatar}, #{message.isRead}, #{message.jumpType}, #{message.jumpUrl}, #{message.relatedId}, 
             #{message.actionText}, #{message.fromUserId}, #{message.conversationId}, #{message.status}, #{message.createTime})
        </foreach>
    </insert>

    <update id="updateMessage" parameterType="Message">
        update message
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="jumpType != null">jump_type = #{jumpType},</if>
            <if test="jumpUrl != null">jump_url = #{jumpUrl},</if>
            <if test="relatedId != null">related_id = #{relatedId},</if>
            <if test="actionText != null">action_text = #{actionText},</if>
            <if test="fromUserId != null">from_user_id = #{fromUserId},</if>
            <if test="conversationId != null">conversation_id = #{conversationId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <update id="markMessagesAsRead">
        update message set is_read = 1, update_time = sysdate()
        where user_id = #{userId} and id in
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </update>

    <update id="markAllMessagesAsRead">
        update message set is_read = 1, update_time = sysdate()
        where user_id = #{userId} and type = #{type} and is_read = 0 and status = 0
    </update>

    <update id="recallMessage">
        update message set status = 2, update_time = sysdate()
        where id = #{messageId} and from_user_id = #{userId}
    </update>

    <delete id="deleteMessageById" parameterType="String">
        update message set status = 1 where id = #{id}
    </delete>

    <delete id="deleteMessageByIds" parameterType="String">
        update message set status = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取会话消息列表 -->
    <select id="getConversationMessages" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        WHERE conversation_id = #{conversationId}
            AND type = 'chat'
            AND status = 0
            AND (user_id = #{userId} OR from_user_id = #{userId})
        ORDER BY create_time DESC
    </select>

    <!-- 获取会话消息总数 -->
    <select id="getConversationMessageCount" resultType="Long">
        SELECT COUNT(*)
        FROM message
        WHERE conversation_id = #{conversationId}
            AND type = 'chat'
            AND status = 0
            AND (user_id = #{userId} OR from_user_id = #{userId})
    </select>

    <!-- 获取消息所属的会话ID集合 -->
    <select id="getConversationIdsByMessageIds" resultType="String">
        SELECT DISTINCT conversation_id 
        FROM message 
        WHERE id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
        AND conversation_id IS NOT NULL
    </select>

    <!-- 获取会话中最新的消息ID -->
    <select id="getLastMessageIdInConversation" resultType="String">
        SELECT id 
        FROM message 
        WHERE conversation_id = #{conversationId} 
            AND status = 0 
        ORDER BY create_time DESC 
        LIMIT 1
    </select>

    <!-- 根据消息ID列表查询消息 -->
    <select id="selectMessagesByIds" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        WHERE id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
    </select>

    <!-- 获取会话中的未读消息 -->
    <select id="getUnreadConversationMessages" resultMap="MessageResult">
        <include refid="selectMessageVo"/>
        WHERE conversation_id = #{conversationId} 
            AND user_id = #{userId}
            AND type = 'chat' 
            AND is_read = 0
            AND status = 0
        ORDER BY create_time ASC
    </select>

</mapper> 