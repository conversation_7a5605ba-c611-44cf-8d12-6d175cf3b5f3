package com.fs.swap.wx.controller;

import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.Message;
import com.fs.swap.common.core.domain.entity.MessageSettings;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.utils.StringUtils;
import com.fs.swap.system.service.IMessageService;
import com.fs.swap.system.service.IMessageSettingsService;
import com.fs.swap.system.service.IConversationService;
import com.fs.swap.system.service.IConversationMemberService;
import com.fs.swap.wx.pojo.dto.MessageDTO;
import com.fs.swap.wx.pojo.dto.SystemNotificationDTO;
import com.fs.swap.wx.pojo.dto.SendUserMessageDTO;
import com.fs.swap.wx.pojo.dto.MessageMuteDTO;
import com.fs.swap.wx.pojo.dto.CreateConversationDTO;
import com.fs.swap.common.core.domain.dto.ConversationListDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息控制器
 * 
 * <AUTHOR>
 * @date 2024
 */
@RestController
@RequestMapping("/message")
public class MessageController extends WxApiBaseController {
    
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Autowired
    private IMessageService messageService;
    
    @Autowired
    private IMessageSettingsService messageSettingsService;

    @Autowired
    private IConversationService conversationService;
    
    @Autowired
    private IConversationMemberService conversationMemberService;

    /**
     * 获取消息列表
     */
    @GetMapping("/list")
    public TableDataInfo getMessageList(MessageDTO messageDTO) {
        try {
            Long userId = getUserId();
            String type = messageDTO.getType();
            
            startPage();
            List<Message> list = messageService.selectUserMessageList(userId, type);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("获取消息列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread-count")
    public AjaxResult getUnreadCount() {
        try {
            Long userId = getUserId();
            Map<String, Integer> unreadCount = messageService.getUserUnreadCount(userId);
            return AjaxResult.success(unreadCount);
        } catch (Exception e) {
            logger.error("获取未读消息数量失败", e);
            return AjaxResult.error("获取未读消息数量失败");
        }
    }

    /**
     * 标记消息已读
     */
    @PostMapping("/mark-read")
    public AjaxResult markAsRead(@RequestBody MessageDTO messageDTO) {
        try {
            Long userId = getUserId();
            List<String> messageIds = messageDTO.getMessageIds();
            
            if (messageIds == null || messageIds.isEmpty()) {
                return AjaxResult.error("消息ID不能为空");
            }
            
            int result = messageService.markMessagesAsRead(messageIds, userId);
            
            Map<String, Object> data = new HashMap<>();
            data.put("successCount", result);
            data.put("failedIds", messageIds.size() > result ? 
                     messageIds.subList(result, messageIds.size()) : null);
            
            return AjaxResult.success("标记成功", data);
        } catch (Exception e) {
            logger.error("标记消息已读失败", e);
            return AjaxResult.error("标记失败，请重试");
        }
    }

    /**
     * 批量标记消息已读
     */
    @PostMapping("/mark-all-read")
    public AjaxResult markAllAsRead(@RequestBody MessageDTO messageDTO) {
        try {
            Long userId = getUserId();
            String type = messageDTO.getType();
            
            if (StringUtils.isEmpty(type)) {
                return AjaxResult.error("消息类型不能为空");
            }
            
            int result = messageService.markAllMessagesAsRead(userId, type);
            return AjaxResult.success("批量标记成功", result);
        } catch (Exception e) {
            logger.error("批量标记消息已读失败", e);
            return AjaxResult.error("批量标记失败，请重试");
        }
    }

    /**
     * 删除消息
     */
    @PostMapping("/delete")
    public AjaxResult deleteMessages(@RequestBody MessageDTO messageDTO) {
        try {
            List<String> messageIds = messageDTO.getMessageIds();
            
            if (messageIds == null || messageIds.isEmpty()) {
                return AjaxResult.error("消息ID不能为空");
            }
            
            String[] ids = messageIds.toArray(new String[0]);
            int result = messageService.deleteMessageByIds(ids);
            return AjaxResult.success("删除成功", result);
        } catch (Exception e) {
            logger.error("删除消息失败", e);
            return AjaxResult.error("删除失败，请重试");
        }
    }

    /**
     * 搜索消息
     */
    @GetMapping("/search")
    public TableDataInfo searchMessages(MessageDTO messageDTO) {
        try {
            Long userId = getUserId();
            String keyword = messageDTO.getKeyword();
            String type = messageDTO.getType();
            
            if (StringUtils.isEmpty(keyword)) {
                return new TableDataInfo();
            }
            
            startPage();
            List<Message> list = messageService.searchMessages(userId, keyword, type);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("搜索消息失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 发送系统通知
     */
    @PostMapping("/send-system")
    public AjaxResult sendSystemNotification(@RequestBody SystemNotificationDTO notificationDTO) {
        try {
            if (StringUtils.isEmpty(notificationDTO.getTitle())) {
                return AjaxResult.error("标题不能为空");
            }
            if (StringUtils.isEmpty(notificationDTO.getContent())) {
                return AjaxResult.error("内容不能为空");
            }
            
            SystemNotificationDTO.JumpConfig jumpConfig = notificationDTO.getJumpConfig();
            String jumpType = jumpConfig != null ? jumpConfig.getJumpType() : "none";
            String jumpUrl = jumpConfig != null ? jumpConfig.getJumpUrl() : null;
            String relatedId = jumpConfig != null ? jumpConfig.getRelatedId() : null;
            String actionText = jumpConfig != null ? jumpConfig.getActionText() : null;
            
            List<Long> targetUserIds = "specific".equals(notificationDTO.getTargetType()) ? 
                                     notificationDTO.getTargetUserIds() : null;
            
            int result = messageService.sendSystemNotification(
                notificationDTO.getTitle(),
                notificationDTO.getContent(),
                notificationDTO.getType(),
                targetUserIds,
                jumpType,
                jumpUrl,
                relatedId,
                actionText
            );
            
            return AjaxResult.success("发送成功", result);
        } catch (Exception e) {
            logger.error("发送系统通知失败", e);
            return AjaxResult.error("发送失败，请重试");
        }
    }

    /**
     * 发送用户消息
     */
    @PostMapping("/send-user")
    public AjaxResult sendUserMessage(@Valid @RequestBody SendUserMessageDTO sendUserMessageDTO) {
        try {
            Long fromUserId = getUserId();
            Long toUserId = sendUserMessageDTO.getToUserId();
            String content = sendUserMessageDTO.getContent();
            String conversationId = sendUserMessageDTO.getConversationId();
            Long productId = sendUserMessageDTO.getProductId();
            
            int result = messageService.sendUserMessage(fromUserId, toUserId, content, conversationId, productId);
            return AjaxResult.success("发送成功", result);
        } catch (Exception e) {
            logger.error("发送用户消息失败", e);
            return AjaxResult.error("发送失败，请重试");
        }
    }

    /**
     * 撤回消息
     */
    @PostMapping("/recall/{messageId}")
    public AjaxResult recallMessage(@PathVariable String messageId) {
        try {
            Long userId = getUserId();
            int result = messageService.recallMessage(messageId, userId);
            return AjaxResult.success("撤回成功", result);
        } catch (Exception e) {
            logger.error("撤回消息失败", e);
            return AjaxResult.error("撤回失败，请重试");
        }
    }

    /**
     * 获取消息设置
     */
    @GetMapping("/settings")
    public AjaxResult getMessageSettings() {
        try {
            Long userId = getUserId();
            MessageSettings settings = messageSettingsService.selectMessageSettingsByUserId(userId);
            return AjaxResult.success(settings);
        } catch (Exception e) {
            logger.error("获取消息设置失败", e);
            return AjaxResult.error("获取消息设置失败");
        }
    }

    /**
     * 更新消息设置
     */
    @PostMapping("/settings")
    public AjaxResult updateMessageSettings(@RequestBody MessageSettings messageSettings) {
        try {
            Long userId = getUserId();
            messageSettings.setUserId(userId);
            
            int result = messageSettingsService.updateMessageSettings(messageSettings);
            return AjaxResult.success("设置更新成功", result);
        } catch (Exception e) {
            logger.error("更新消息设置失败", e);
            return AjaxResult.error("更新设置失败，请重试");
        }
    }

    /**
     * 设置消息免打扰
     */
    @PostMapping("/mute")
    public AjaxResult setMessageMute(@Valid @RequestBody MessageMuteDTO messageMuteDTO) {
        try {
            Long userId = getUserId();
            String type = messageMuteDTO.getType();
            Boolean mute = messageMuteDTO.getMute();
            
            int result = messageSettingsService.setMessageMute(userId, type, mute);
            return AjaxResult.success("设置成功", result);
        } catch (Exception e) {
            logger.error("设置消息免打扰失败", e);
            return AjaxResult.error("设置失败，请重试");
        }
    }

    /**
     * 获取会话列表（重构后基于会话表）
     */
    @GetMapping("/conversations")
    public AjaxResult getConversationList(MessageDTO messageDTO) {
        try {
            Long userId = getUserId();
            
            // 获取用户的所有聊天会话（基于会话表的优化实现）
            List<ConversationListDTO> conversations = conversationService.getUserConversations(userId);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", conversations);
            data.put("total", conversations.size());
            data.put("hasMore", false);
            
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取会话列表失败", e);
            return AjaxResult.error("获取会话列表失败");
        }
    }

    /**
     * 获取会话消息
     */
    @GetMapping("/conversation/{conversationId}/messages")
    public AjaxResult getConversationMessages(@PathVariable String conversationId, MessageDTO messageDTO) {
        try {
            Long userId = getUserId();

            // 设置默认分页参数：聊天默认加载最新10条消息
            if (messageDTO.getPageNum() == null) {
                messageDTO.setPageNum(1);
            }
            if (messageDTO.getPageSize() == null) {
                messageDTO.setPageSize(10);
            }

            startPage();
            List<Message> messages = messageService.getConversationMessages(conversationId, userId);

            // 获取总消息数量用于判断是否还有更多
            Long totalCount = messageService.getConversationMessageCount(conversationId, userId);
            boolean hasMore = (long) messageDTO.getPageNum() * messageDTO.getPageSize() < totalCount;

            Map<String, Object> data = new HashMap<>();
            data.put("list", messages);
            data.put("hasMore", hasMore);
            data.put("total", totalCount);

            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取会话消息失败", e);
            return AjaxResult.error("获取会话消息失败");
        }
    }

    /**
     * 创建或获取会话（重构后使用会话表）
     */
    @PostMapping("/conversation/create")
    public AjaxResult createConversation(@Valid @RequestBody CreateConversationDTO createConversationDTO) {
        try {
            Long userId = getUserId();
            Long targetUserId = createConversationDTO.getTargetUserId();
            
            // 使用会话服务创建或获取会话
            String conversationId = conversationService.createOrGetSingleConversation(userId, targetUserId);
            
            Map<String, Object> data = new HashMap<>();
            data.put("conversationId", conversationId);
            
            return AjaxResult.success("会话创建成功", data);
        } catch (Exception e) {
            logger.error("创建会话失败", e);
            return AjaxResult.error("创建会话失败");
        }
    }

    /**
     * 设置会话免打扰
     */
    @PostMapping("/conversation/{conversationId}/mute")
    public AjaxResult setConversationMute(@PathVariable String conversationId, 
                                        @RequestParam Integer isMuted) {
        try {
            Long userId = getUserId();
            
            int result = conversationMemberService.updateMuteStatus(conversationId, userId, isMuted);
            
            return AjaxResult.success("设置免打扰成功", result);
        } catch (Exception e) {
            logger.error("设置会话免打扰失败", e);
            return AjaxResult.error("设置免打扰失败");
        }
    }

    /**
     * 标记会话消息已读
     */
    @PostMapping("/conversation/{conversationId}/mark-read")
    public AjaxResult markConversationAsRead(@PathVariable String conversationId) {
        try {
            Long userId = getUserId();
            
            if (conversationId == null || conversationId.isEmpty()) {
                return AjaxResult.error("会话ID不能为空");
            }
            
            // 获取该会话中用户的所有未读消息
            List<Message> unreadMessages = messageService.getUnreadConversationMessages(conversationId, userId);
            
            if (!unreadMessages.isEmpty()) {
                List<String> messageIds = unreadMessages.stream()
                    .map(Message::getId)
                    .collect(java.util.stream.Collectors.toList());
                
                // 标记这些消息为已读
                int result = messageService.markMessagesAsRead(messageIds, userId);
                
                Map<String, Object> data = new HashMap<>();
                data.put("markedCount", result);
                data.put("conversationId", conversationId);
                
                return AjaxResult.success("标记成功", data);
            }
            
            return AjaxResult.success("没有未读消息");
        } catch (Exception e) {
            logger.error("标记会话消息已读失败", e);
            return AjaxResult.error("标记失败，请重试");
        }
    }
} 