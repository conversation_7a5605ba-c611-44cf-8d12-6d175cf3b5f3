# 项目相关配置
swap:

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8081
  servlet:
    # 应用的访问路径
    context-path: /wx

# Spring配置
spring:
  profiles:
    active: devpro
    include:
      - common
# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.fs.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: 3c8a7d9e5b12f8c761ad09e27885de4a
  # 令牌有效期（默认7天）
  expireTime: 7

# 公众号配置(必填)
wx:
  miniapp:
    appid: wx24725a903aea9a43
    secret: e8b6d98b29c1979ed261d0e2a821071f
    token: notHave
    aesKey: notHave
    msgDataFormat: notHave                 # 消息格式，XML或者JSON.
    config-storage:
      # 存储配置redis(可选)
      # 注意: 指定redis.host值后不会使用容器注入的redis连接(JedisPool)
      type: RedisTemplate                     # 配置类型: Memory(默认), Jedis, RedisTemplate
      key-prefix: swap                 # 相关redis前缀配置: wa(默认)
      # http客户端配置
      http-client-type: HttpClient      # http客户端类型: HttpClient(默认), OkHttp, JoddHttp
      http-proxy-host:
      http-proxy-port:
      http-proxy-username:
      http-proxy-password:
  mp:
    # 公众号配置(必填)
    app-id: wx1b6288672204dcef
    secret: 0f62ca0fe62ce47e500fbebcb8aea8d3
    token: 0lCVgjlYifw15fszWRsq
    aes-key: vo1PSCP5mIXXjAqtgi6LC8lN1Xpsdd9E9P58eRszbR5
    use-stable-access-token: false
    config-storage:
      # 存储配置redis(可选)
      type: RedisTemplate                     # 配置类型: Memory(默认), Jedis, RedisTemplate
      key-prefix: swap-mp                  # 相关redis前缀配置: wx(默认)
      # http客户端配置
      http-client-type: httpclient      # http客户端类型: HttpClient(默认), OkHttp, JoddHttp
      http-proxy-host:
      http-proxy-port:
      http-proxy-username:
      http-proxy-password:

tencent:
  map:
    key: UQQBZ-4X4YQ-Z735T-26KFT-Z6AOO-3XF67
    sk: KP7sO7CcRhGLC9cU4YpQcpejZSVZaifx

# 请求日志配置
request:
  log:
    # 是否启用请求日志记录
    enabled: true
    # 是否记录请求参数
    log-params: true
    # 是否记录响应结果
    log-response: true
    # 参数日志最大长度
    max-param-length: 500
    # 响应日志最大长度
    max-response-length: 1000
    # 是否记录IP地址
    log-ip: true
    # 是否记录执行时间
    log-execution-time: true
    # 慢请求阈值（毫秒），超过此时间的请求会标记为慢请求
    slow-request-threshold: 1000
