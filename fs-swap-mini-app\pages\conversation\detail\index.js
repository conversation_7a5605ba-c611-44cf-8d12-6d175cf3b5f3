const messageService = require('../../../services/message')
const systemInfoService = require('../../../services/systemInfo')
const { formatTime } = require('../../../utils/dateUtil')

// 常量定义
const DEFAULT_AVATAR = '/static/img/default_avatar.png'

Page({
  data: {
    conversationId: '',
    title: '',
    messageList: [],
    inputText: '',
    isLoading: false,
    isSending: false,
    // 仅记录会话关联商品ID，用于发送上下文消息
    productId: null,
    // 分页相关
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    isLoadingMore: false
  },

  // 简单定时器配置
  refreshTimer: null,
  refreshInterval: 15000, // 15秒刷新一次

  // 运行时上下文消息发送防抖Map
  _ctxPendingMap: {},
  // 商品信息缓存，减少重复请求
  _productInfoCache: {},

  onLoad(options) {
    let { conversationId, title, userId, nickname, sellerId, productId } = options
    
    // 如果传入的是sellerId（从商品详情页面跳转）
    if (!conversationId && sellerId) {
      const currentUserId = this.getCurrentUserId()
      // 生成会话ID格式：conv_小ID_大ID
      const userIds = [parseInt(currentUserId), parseInt(sellerId)].sort((a, b) => a - b)
      conversationId = `conv_${userIds[0]}_${userIds[1]}`
      title = '与卖家聊天'
      
      // 存储相关信息
      this.setData({
        productId: productId || null
      })
    }
    // 如果传入的是userId（从订单页面跳转），需要生成conversationId
    else if (!conversationId && userId) {
      const currentUserId = this.getCurrentUserId()
      // 生成会话ID格式：conv_小ID_大ID
      const userIds = [parseInt(currentUserId), parseInt(userId)].sort((a, b) => a - b)
      conversationId = `conv_${userIds[0]}_${userIds[1]}`
      title = nickname || '聊天'
    }
    
    this.setData({
      conversationId: conversationId || '',
      title: title || '聊天'
    })
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: title || '聊天'
    })
    
    // 只有当conversationId存在时才加载消息
    if (conversationId) {
      this.loadMessages()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
    }
  },

  onShow() {
    // 页面显示时刷新消息（重置到第一页）
    this.loadMessages(true)
    // 启动自动刷新
    this.startAutoRefresh()
    // 标记会话中的消息为已读
    this.markConversationAsRead()
  },

  onHide() {
    // 页面隐藏时停止自动刷新
    this.stopAutoRefresh()
  },

  onUnload() {
    // 页面销毁时停止自动刷新
    this.stopAutoRefresh()
  },

  /**
   * 启动自动刷新
   */
  startAutoRefresh() {
    this.stopAutoRefresh() // 先停止之前的定时器
    
    this.refreshTimer = setInterval(() => {
      this.silentRefreshMessages()
    }, this.refreshInterval)
    
    },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
      }
  },

  /**
   * 静默刷新消息（定时器专用）
   * 只检查是否有新消息，不影响分页状态
   */
  async silentRefreshMessages() {
    try {
      // 静默刷新只获取第一页最新消息来检查是否有新消息
      const result = await messageService.getConversationMessages(this.data.conversationId, {
        pageNum: 1,
        pageSize: this.data.pageSize
      })
      
      // 获取当前用户信息
      const app = getApp()
      const currentUserInfo = app.globalData.userInfo || {}
      const currentUserId = this.getCurrentUserId()
      
      const processedList = await Promise.all(result.list.map(async item => {
        const isMine = item.fromUserId === currentUserId
        
        let avatar = null
        let myAvatar = null
        let nickname = null
        let myNickname = null
        
        if (isMine) {
          // 我的消息：处理我的头像和昵称
          if (currentUserInfo.avatar) {
            myAvatar = await systemInfoService.processImageUrl(currentUserInfo.avatar)
          } else {
            myAvatar = DEFAULT_AVATAR
          }
          myNickname = currentUserInfo.nickname || '我'
        } else {
          // 别人的消息：处理对方头像和昵称
          if (item.avatar || item.fromUserAvatar) {
            avatar = await systemInfoService.processImageUrl(item.avatar || item.fromUserAvatar)
          } else {
            avatar = DEFAULT_AVATAR
          }
          nickname = item.nickname || item.fromUserNickname || '用户'
        }
        
        const processedItem = {
          ...item,
          timeText: formatTime(item.createTime),
          isMine: isMine,
          avatar: avatar,
          myAvatar: myAvatar,
          nickname: nickname,
          myNickname: myNickname
        }

        // 识别商品卡片消息（优先解析内容中的JSON或对象）
        try {
          let obj = null
          if (typeof item.content === 'string') {
            obj = JSON.parse(item.content)
          } else if (item.content && typeof item.content === 'object') {
            obj = item.content
          }
          if (obj && obj._type === 'product' && obj.productId) {
            const productId = String(obj.productId)
            const info = await this.getProductInfoForCard(productId)
            if (info) {
              processedItem.isProductCard = true
              processedItem.productCard = {
                productId,
                title: (info.desc || '商品').substring(0, 5),
                cover: info.cover || '',
                price: info.price == 0 ? '免费送' : (info.price || '')
              }
            }
          }
        } catch (e) { /* 普通文本或非JSON内容，忽略 */ }

        return processedItem
      }))
      
      // 检查是否有新消息（静默刷新只检查最新的消息）
      const currentMessages = this.data.messageList
      const latestCurrentMessage = currentMessages[currentMessages.length - 1]
      const latestNewMessage = processedList[processedList.length - 1]

      // 判断是否有新消息：比较最新消息的ID
      const hasNewMessages = !latestCurrentMessage ||
        (latestNewMessage && latestNewMessage.id !== latestCurrentMessage.id)

      if (hasNewMessages) {
        // 只添加新消息到现有列表末尾，不替换整个列表
        const newMessages = processedList.filter(newMsg =>
          !currentMessages.some(currentMsg => currentMsg.id === newMsg.id)
        )

        if (newMessages.length > 0) {
          this.setData({
            messageList: [...currentMessages, ...newMessages]
          })

          // 回填商品ID（仅在未带入商品时）
          const lastWithProduct = [...newMessages].reverse().find(m => m.isProductCard)
          if (!this.data.productId && lastWithProduct) {
            this.setData({ productId: String(lastWithProduct.productCard.productId) })
          }

          // 自动滚动到底部
          this.scrollToBottom()

          // 轻微振动提示有新消息
          wx.vibrateShort({
            type: 'light'
          })

          // 标记为已读
          setTimeout(() => {
            this.markConversationAsRead()
          }, 200)
        }
      }

    } catch (error) {
      // 静默刷新失败，不影响用户体验
    }
  },



  /**
   * 发送消息后立即刷新
   */
  refreshAfterSend() {
    // 发送消息后，立即刷新一次获取最新状态
    setTimeout(() => {
      this.silentRefreshMessages()
    }, 500)
  },

  /**
   * 加载聊天消息
   */
  async loadMessages(reset = true) {
    try {
      this.setData({ isLoading: true })

      // 如果是重置加载，重置分页参数
      if (reset) {
        this.setData({
          pageNum: 1,
          messageList: []
        })
      }

      const result = await messageService.getConversationMessages(this.data.conversationId, {
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      })
      // 获取当前用户信息
      const app = getApp()
      const currentUserInfo = app.globalData.userInfo || {}
      const currentUserId = this.getCurrentUserId()
      // 后端返回的是倒序（最新在前），需要反转为正序（最新在后）用于聊天显示
      const reversedList = [...result.list].reverse()

      const processedList = await Promise.all(reversedList.map(async item => {
        const isMine = item.fromUserId === currentUserId

        let avatar = null
        let myAvatar = null
        let nickname = null
        let myNickname = null

        if (isMine) {
          // 我的消息：处理我的头像和昵称
          if (currentUserInfo.avatar) {
            myAvatar = await systemInfoService.processImageUrl(currentUserInfo.avatar)
          } else {
            myAvatar = DEFAULT_AVATAR
          }
          myNickname = currentUserInfo.nickname || '我'
        } else {
          // 别人的消息：处理对方头像和昵称
          if (item.avatar || item.fromUserAvatar) {
            avatar = await systemInfoService.processImageUrl(item.avatar || item.fromUserAvatar)
          } else {
            avatar = DEFAULT_AVATAR
          }
          nickname = item.nickname || item.fromUserNickname || '用户'
        }

        const processedItem = {
          ...item,
          timeText: formatTime(item.createTime),
          isMine: isMine,
          avatar: avatar,
          myAvatar: myAvatar,
          nickname: nickname,
          myNickname: myNickname
        }

        // 识别商品卡片消息（优先解析内容中的JSON或对象）
        try {
          let obj = null
          if (typeof item.content === 'string') {
            obj = JSON.parse(item.content)
          } else if (item.content && typeof item.content === 'object') {
            obj = item.content
          }
          if (obj && obj._type === 'product' && obj.productId) {
            const productId = String(obj.productId)
            const info = await this.getProductInfoForCard(productId)
            if (info) {
              processedItem.isProductCard = true
              processedItem.productCard = {
                productId,
                title: (info.desc || '商品').length > 10 ? (info.desc || '商品').substring(0, 10) + '...' : (info.desc || '商品'),
                cover: info.cover || '',
                price: info.price == 0 ? '免费送' : (info.price || '')
              }
            }
          }
        } catch (e) { /* 普通文本消息，忽略 */ }

        return processedItem
      }))

      // 如果是重置加载，直接设置新数据；如果是加载更多，则合并数据
      const currentMessages = reset ? [] : this.data.messageList
      const newMessageList = reset ? processedList : [...processedList, ...currentMessages]

      this.setData({
        messageList: newMessageList,
        hasMore: result.hasMore || false
      })
      
      // 从消息中解析最近一次携带的商品ID（仅在未带入商品时）
      const lastWithProduct = [...processedList].reverse().find(m => m.isProductCard)
      const currentPid = this.data.productId
      if (!currentPid && lastWithProduct) {
        this.setData({ productId: String(lastWithProduct.productCard.productId) })
      }

      // 空会话且带商品：自动发送一次商品卡片
      if ((processedList?.length || 0) === 0 && this.data.productId) {
        this.trySendProductContextIfEmpty()
      }

      // 有历史时：若本次带入了商品，且历史中无该商品卡片或最后一次商品不同，则发送一次商品卡片
      const needSendChanged = (processedList?.length || 0) > 0 && this.data.productId && (
        !lastWithProduct || String(this.data.productId) !== String(lastWithProduct.productCard.productId)
      )
      if (needSendChanged) {
        this.trySendProductContextIfChanged()
      }
      
      // 如果是重置加载，滚动到底部；如果是加载更多，保持当前位置
      if (reset) {
        this.scrollToBottom()

        // 标记新的未读消息为已读
        setTimeout(() => {
          this.markConversationAsRead()
        }, 100)
      }

    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  /**
   * 加载更多历史消息
   */
  async loadMoreMessages() {
    if (this.data.isLoadingMore || !this.data.hasMore) {
      return
    }

    try {
      this.setData({ isLoadingMore: true })

      // 增加页码
      const nextPageNum = this.data.pageNum + 1
      this.setData({ pageNum: nextPageNum })

      // 加载更多消息（不重置）
      await this.loadMessages(false)

    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoadingMore: false })
    }
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  /**
   * 发送消息
   */
  async sendMessage() {
    const { inputText, conversationId, isSending } = this.data
    
    if (!inputText.trim() || isSending) {
      return
    }
    
    try {
      this.setData({ isSending: true })
      
      // 先添加消息到本地列表（优化用户体验）
      const app = getApp()
      const currentUserInfo = app.globalData.userInfo || {}
      
      // 处理我的头像
      let myAvatar = DEFAULT_AVATAR
      if (currentUserInfo.avatar) {
        myAvatar = await systemInfoService.processImageUrl(currentUserInfo.avatar)
      }
      
      const tempMessage = {
        id: 'temp_' + Date.now(),
        content: inputText.trim(),
        isMine: true,
        timeText: '发送中...',
        fromUserId: this.getCurrentUserId(),
        sending: true,
        myAvatar: myAvatar,
        myNickname: currentUserInfo.nickname || '我'
      }
      
      this.setData({
        messageList: [...this.data.messageList, tempMessage],
        inputText: ''
      })
      
      this.scrollToBottom()
      
      // 发送消息到服务器
      await messageService.sendUserMessage({
        toUserId: this.getTargetUserId(),
        content: inputText.trim(),
        conversationId: conversationId,
        // 携带当前会话商品ID（可为空）
        productId: this.data.productId || undefined
      })
      
      // 重新加载消息列表（重置到第一页）
      await this.loadMessages(true)

    } catch (error) {
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      })
      
      // 移除发送失败的临时消息
      const messageList = this.data.messageList.filter(item => !item.sending)
      this.setData({ messageList })
      
    } finally {
      this.setData({ isSending: false })
    }
  },

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    setTimeout(() => {
      this.setData({
        scrollToView: 'message-bottom'
      })
    }, 100)
  },

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    const app = getApp()
    const userInfo = app.safeGetGlobalData('userInfo')
    return userInfo ? userInfo.id : 0
  },

  /**
   * 获取目标用户ID（从会话ID中解析）
   */
  getTargetUserId() {
    const { conversationId } = this.data
    const currentUserId = this.getCurrentUserId()

    if (!conversationId) {
      return 0
    }

    // 解析会话ID：conv_userId1_userId2
    const parts = conversationId.split('_')
    if (parts.length >= 3) {
      const userId1 = parseInt(parts[1])
      const userId2 = parseInt(parts[2])
      return userId1 === currentUserId ? userId2 : userId1
    }

    return 0
  },





  /**
   * 头像加载错误处理
   */
  async onAvatarError(e) {
    const { type, index } = e.currentTarget.dataset
    
    // 更新对应消息的头像为默认头像
    const messageList = [...this.data.messageList]
    if (messageList[index]) {
      if (type === 'other') {
        messageList[index].avatar = DEFAULT_AVATAR
      } else if (type === 'mine') {
        messageList[index].myAvatar = DEFAULT_AVATAR
      }
      
      this.setData({
        messageList: messageList
      })
    }
  },

  /**
   * 标记会话中的消息为已读
   */
  async markConversationAsRead() {
    try {
      const { messageList } = this.data
      const currentUserId = this.getCurrentUserId()
      
      // 找出所有未读的对方消息
      const unreadMessages = messageList.filter(item => 
        !item.isMine && !item.isRead && item.fromUserId !== currentUserId
      )
      
      if (unreadMessages.length > 0) {
        const messageIds = unreadMessages.map(item => item.id)
        await messageService.markAsRead(messageIds)
        
        // 更新本地消息状态
        this.updateMessagesReadStatus(messageIds)
      }
    } catch (error) {
      // 标记已读失败，不影响用户体验
    }
  },

  /**
   * 更新本地消息已读状态
   */
  updateMessagesReadStatus(messageIds) {
    const updatedList = this.data.messageList.map(item => {
      if (messageIds.includes(item.id)) {
        return { ...item, isRead: true }
      }
      return item
    })
    
    this.setData({
      messageList: updatedList
    })
  },



  /**
   * 检测历史消息中是否已存在该商品卡片
   */
  hasProductCardInHistory(productId) {
    try {
      if (!productId) return false
      const pid = String(productId)
      const list = this.data.messageList || []
      const recent = list.slice(-10)
      return recent.some(m => m.isProductCard && String(m.productCard?.productId) === pid)
    } catch (e) {
      return false
    }
  },

  /**
   * 获取商品信息（用于卡片渲染，带缓存）
   */
  async getProductInfoForCard(productId) {
    try {
      if (!productId) return null
      if (this._productInfoCache[productId]) return this._productInfoCache[productId]
      const api = require('../../../config/api')
      const res = await api.getProductDetail(productId)
      const raw = res?.data || res || {}
      // 兼容 images 为字符串或数组的情况，优先 cover/image
      let cover = raw.cover || raw.image
      if (!cover) {
        if (raw.images) {
          if (Array.isArray(raw.images)) {
            cover = raw.images[0]
          } else if (typeof raw.images === 'string') {
            const arr = raw.images.split(',').map(s => s.trim()).filter(Boolean)
            cover = arr[0]
          }
        } else if (Array.isArray(raw.imageList)) {
          cover = raw.imageList[0]
        }
      }
      const processedCover = cover ? await systemInfoService.processImageUrl(cover) : ''
      const info = {
        id: raw.id || productId,
        title: raw.title || raw.name || raw.productName || '商品',
        price: raw.price || raw.points || '',
        cover: processedCover,
        desc: raw.description || raw.desc || raw.content || raw.summary || ''
      }
      this._productInfoCache[productId] = info
      return info
    } catch (e) {
      // 获取商品信息失败
    }
  },

  /**
   * 若会话为空且携带商品，则自动发一条商品卡片（仅一次）
   */
  async trySendProductContextIfEmpty() {
    try {
      const { conversationId, productId, isSending } = this.data
      if (!conversationId || !productId || isSending) return
      // 历史检查：若已有卡片则不发
      if (this.hasProductCardInHistory(productId)) return
      const key = `conv_ctx_${conversationId}_${productId}`

      // 运行时防抖
      if (this._ctxPendingMap[key]) return
      this._ctxPendingMap[key] = true

      // 本地去重（仅作为弱去重）
      const sent = wx.getStorageSync(key)
      if (sent) { delete this._ctxPendingMap[key]; return }

      // 仅发送最小JSON（不携带商品完整信息）
      const content = JSON.stringify({ _type: 'product', productId })
      await messageService.sendUserMessage({
        toUserId: this.getTargetUserId(),
        content,
        conversationId,
        productId
      })
      // 发送成功后再写入去重标记
      wx.setStorageSync(key, 1)
      this.refreshAfterSend()
    } catch (e) {
      console.warn('自动发送商品卡片失败', e)
    } finally {
      // 清理防抖标记
      const { conversationId, productId } = this.data
      const key = `conv_ctx_${conversationId}_${productId}`
      delete this._ctxPendingMap[key]
    }
  },

  /**
   * 历史存在但用户切换到了新商品时，自动发一次商品卡片（仅一次）
   */
  async trySendProductContextIfChanged() {
    try {
      const { conversationId, productId, isSending } = this.data
      if (!conversationId || !productId || isSending) return
      // 历史检查：若已有该商品卡片则不发
      if (this.hasProductCardInHistory(productId)) return
      const key = `conv_ctx_${conversationId}_${productId}`

      // 运行时防抖
      if (this._ctxPendingMap[key]) return
      this._ctxPendingMap[key] = true

      // 本地去重（仅作为弱去重）
      const sent = wx.getStorageSync(key)
      if (sent) { delete this._ctxPendingMap[key]; return }

      const content = JSON.stringify({ _type: 'product', productId })
      await messageService.sendUserMessage({
        toUserId: this.getTargetUserId(),
        content,
        conversationId,
        productId
      })
      // 成功后写入去重
      wx.setStorageSync(key, 1)
      this.refreshAfterSend()
    } catch (e) {
      console.warn('自动发送商品卡片(变更)失败', e)
    } finally {
      // 清理防抖标记
      const { conversationId, productId } = this.data
      const key = `conv_ctx_${conversationId}_${productId}`
      delete this._ctxPendingMap[key]
    }
  },

  /** 点击消息内商品卡片 */
  onTapMessageProductCard(e) {
    const pid = e.currentTarget.dataset.productId
    if (!pid) return
    wx.navigateTo({ url: `/pages/item/detail?id=${pid}` })
  }
}) 